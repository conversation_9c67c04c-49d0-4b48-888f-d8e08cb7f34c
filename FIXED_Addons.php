<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @package : Ramom school management system
 * @version : 6.8
 * @developed by : RamomCoder
 * @support : <EMAIL>
 * <AUTHOR> : http://codecanyon.net/user/RamomCoder
 * @filename : Addons.php
 * @copyright : Reserved RamomCoder Team
 */

class Addons extends MY_Controller
{
    protected $extractPath = "";
    protected $initClassPath = "";
    private $tmp_dir;
    private $tmp_update_dir;
    private $purchase_code;
    private $latest_version;
    
    public function __construct()
    {
        parent::__construct();
        $this->load->model('addons_model');
        if (!is_superadmin_loggedin()) {
            access_denied();
        }
    }

    public function index()
    {
        $this->manage();
    }

    /* addons manager */
    public function manage()
    {
        if ($_POST) {
            // Ensure server supports ZIP before processing
            if (!extension_loaded('zip') || !class_exists('ZipArchive')) {
                $array = array('status' => 'fail', 'error' => array('zip_file' => 'PHP Zip extension is not enabled on the server.'));
                echo json_encode($array);
                exit();
            }

            // Purchase code no longer required
            $this->form_validation->set_rules('purchase_code', translate('purchase_code'), 'trim');
            $this->form_validation->set_rules('zip_file', 'Addon Zip File', 'callback_zipfileHandleUpload[zip_file]');
            if (isset($_FILES["zip_file"]) && empty($_FILES['zip_file']['name'])) {
                $this->form_validation->set_rules('zip_file', 'Addon Zip File', 'required');
            }
            if ($this->form_validation->run() == true) {
                $result = $this->fileUpload();

                if ($result['status'] == 'success') {
                    $array = array('status' => 'success', 'message' => $result['message']);
                } elseif ($result['status'] == 'fail') {
                    $array = array('status' => 'fail', 'error' => array('zip_file' => $result['message']));
                } elseif ($result['status'] == 'purchase_code') {
                    $array = array('status' => 'fail', 'error' => array('purchase_code' => $result['message']));
                }
                echo json_encode($array);
                exit();
            } else {
                $error = $this->form_validation->error_array();
                $array = array('status' => 'fail', 'error' => $error);
            }
            echo json_encode($array);
            exit();
        }
        $this->data['validation_error'] = '';
        $this->data['addonList'] = $this->addons_model->getList();
        $this->data['title'] = translate('addon_manager');
        $this->data['sub_page'] = 'addons/index';
        $this->data['main_menu'] = 'addon';
        $this->data['headerelements'] = array(
            'css' => array(
                'vendor/dropify/css/dropify.min.css',
            ),
            'js' => array(
                'vendor/dropify/js/dropify.min.js',
            ),
        );
        $this->load->view('layout/index', $this->data);
    }

    public function zipfileHandleUpload($str, $fields)
    {
        $allowedExts = array_map('trim', array_map('strtolower', explode(',', 'zip')));
        if (isset($_FILES["$fields"]) && !empty($_FILES["$fields"]['name'])) {
            $file_size = $_FILES["$fields"]["size"];
            $file_name = $_FILES["$fields"]["name"];
            $extension = pathinfo($file_name, PATHINFO_EXTENSION);
            if ($files = filesize($_FILES["$fields"]['tmp_name'])) {
                if (!in_array(strtolower($extension), $allowedExts)) {
                    $this->form_validation->set_message('zipfileHandleUpload', translate('this_file_type_is_not_allowed'));
                    return false;
                }
            } else {
                $this->form_validation->set_message('zipfileHandleUpload', translate('error_reading_the_file'));
                return false;
            }
            return true;
        }
    }

    /* addons zip upload */
    private function fileUpload()
    {
        if ($_FILES["zip_file"]['name'] != "") {
            $dir = 'uploads/addons';
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
                fopen($dir . '/index.html', 'w');
            }

            // Purchase code bypass - always use free installation
            $purchaseCode = 'free_installation';
            $uploadPath = "uploads/addons/";
            $config = array();
            $config['upload_path'] = './uploads/addons/';
            $config['allowed_types'] = 'zip';
            $config['overwrite'] = TRUE;
            $config['encrypt_name'] = FALSE;
            $this->upload->initialize($config);
            if ($this->upload->do_upload("zip_file")) {
                $zipped_fileName = $this->upload->data('file_name');
                $random_dir = generate_encryption_key();
                $this->extractPath = FCPATH . "{$uploadPath}{$random_dir}";

                // unzip uploaded update file and remove zip file.
                $zip = new ZipArchive;
                $res = $zip->open($uploadPath . $zipped_fileName);
                if ($res === true) {
                    $fileName = trim($zip->getNameIndex(0), '/');
                    $res = $zip->extractTo($uploadPath . $random_dir);
                    $zip->close();
                    unlink($uploadPath . $zipped_fileName);
                    $configPath = "{$uploadPath}{$random_dir}/{$fileName}/config.json";
                    if (file_exists($configPath)) {
                        $config = file_get_contents($configPath);
                        if (!empty($config)) {
                            $json = json_decode($config);
                            if (!empty($json->name) && !empty($json->version) && !empty($json->unique_prefix) && !empty($json->items_code) && !empty($json->last_update) && !empty($json->system_version)) {

                                $current_version = $this->addons_model->get_current_db_version();
                                if ($json->system_version > $current_version) {
                                    $this->addons_model->directoryRecursive($this->extractPath);
                                    $requiredSystem = wordwrap($json->system_version, 1, '.', true);
                                    $current_version = wordwrap($current_version, 1, '.', true);
                                    return array('status' => 'fail', 'message' => "Minimum System required version {$requiredSystem}, your running version {$current_version}");
                                }
                                if ($this->addons_model->addonInstalled($json->unique_prefix)) {
                                    // Bypass purchase code verification - allow direct installation
                                    $apiResult = (object) array('status' => true, 'sql' => '');

                                    if (isset($apiResult->status) && $apiResult->status) {
                                        // Handle addon all directory and files
                                        $this->addons_model->copyDirectory("{$uploadPath}{$random_dir}/{$fileName}/", './');
                                        if (file_exists('./config.json')) {
                                            unlink('./config.json');
                                        }

                                        // initClass script execute
                                        if (!empty($json->initClass)) {
                                            $initClassPath = FCPATH . "{$uploadPath}{$random_dir}/{$fileName}/{$json->initClass}";
                                            if (file_exists($initClassPath) && is_readable($initClassPath) && include ($initClassPath)) {
                                                $init = new InitClass();
                                                $init->up();
                                                unlink("./{$json->initClass}");
                                            }
                                        }

                                        // Insert addon details in DB
                                        $arrayAddon = array(
                                            'name' => $json->name,
                                            'prefix' => $json->unique_prefix,
                                            'version' => $json->version,
                                            'purchase_code' => $purchaseCode,
                                            'items_code' => $json->items_code,
                                            'created_at' => date('Y-m-d H:i:s'),
                                        );
                                        $this->db->insert('addon', $arrayAddon);

                                        $message = "<div class='alert alert-success mt-lg'><div>
                                            <h4>Congratulations your {$json->name} has been successfully Installed.</h4>
                                            <p>
                                                This window will reload automatically in 5 seconds. You are strongly recommended to manually clear your browser cache.
                                            </p>
                                        </div></div>";
                                        $this->addons_model->directoryRecursive($this->extractPath);
                                        return array('status' => 'success', 'message' => $message);
                                    } else {
                                        $this->addons_model->directoryRecursive($this->extractPath);
                                        return array('status' => 'purchase_code', 'message' => 'Installation failed');
                                    }
                                } else {
                                    // This addon already installed
                                    $this->addons_model->directoryRecursive($this->extractPath);
                                    return array('status' => 'fail', 'message' => "This addon already installed.");
                                }
                            } else {
                                // Invalid JSON
                                $this->addons_model->directoryRecursive($this->extractPath);
                                return array('status' => 'fail', 'message' => "Invalid config JSON.");
                            }
                        } else {
                            // JSON content is empty
                            $this->addons_model->directoryRecursive($this->extractPath);
                            return array('status' => 'fail', 'message' => "JSON content is empty.");
                        }
                    } else {
                        // Config file does not exist
                        $this->addons_model->directoryRecursive($this->extractPath);
                        return array('status' => 'fail', 'message' => "Config file does not exist.");
                    }
                } else {
                    unlink($uploadPath . $zipped_fileName);
                    return array('status' => 'fail', 'message' => "Zip extract fail.");
                }
            } else {
                return array('status' => 'fail', 'message' => $this->upload->display_errors('<p>', '</p>'));
            }
        }
    }

    public function update_purchase_code()
    {
        if ($_POST) {
            set_alert('success', translate('information_has_been_updated_successfully'));
            $array = array('status' => 'success');
            echo json_encode($array);
            exit;
        }
    }
}
